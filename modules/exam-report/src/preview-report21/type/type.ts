export enum ModuleEnum {
    Cover = '1',
    EvaluationIntroduction = '19',
    ComprehensiveResult = '3',
    PersonalityResultOverview = '20',
    PersonalityDetailResult = '21',
    KeyPotentialQualities = '22',
    MatchingDegree = '23',
    TeamRole = '24',
    InterviewSuggestions = '5',
    Reference = '6',
}

export enum LevelEnum {
    High = 3,
    Medium = 2,
    Low = 1,
}

export interface ISubDimensionItem {
    dimensionName: string;
    showName: string;
    definition: string;
    level: number;
    score: number | string;
    levelStr: string;
    levelDescription: string;
}

type LevelType = LevelEnum;
export interface IDimensionItem {
    dimensionId: number;
    dimensionName: string;
    definition: string;
    level: number;
    score: number | string;
    levelStr: string;
    levelDescription: string;
    subDimensionDetails: ISubDimensionItem[];
}

export interface IExamineeInfoItem {
    encryptFieldId: string;
    fieldName: string;
    fieldShowName: string;
    fieldValue: string;
}

export interface IIndicativeItem {
    encryptIndicatorId: string;
    indicatorName: string;
    showName: string;
    indicatorMeaning: string;
    result: string;
}

export interface IInterviewSuggestionsItem {
    dimensionName: string;
    showName: string;
    score: number;
    level: number;
    interviewQuestions: string;
    keyPoints: string;
}

export interface IData extends Record<string, any> {
    examineeInfoList: IExamineeInfoItem[];
    testDescription: string;
    totalScore: number | string;
    levelStr: string;
    levelLanguage: string;
    totalScoreLevel: LevelType;
    totalLevelStr: string;
    totalLevelDescription: string;
    levelArray: number[];
    dimensionDetails: IDimensionItem[];
    indicativeResultLevel: number;
    indicativeResultDesc: string;
    usageInstructions: string;
    indicativeList: IIndicativeItem[];
    readingInstructions: string;
    interviewSuggestions: IInterviewSuggestionsItem[];
}

export interface IProps {
    data?: IData;
    pageFooterData?: string[];
    headerFooterConfig?: { header: boolean; footer: boolean };
}

export interface IExamineeInfo {
    fieldShowName: string;
    fieldValue: string;
}

export interface ICoverData {
    templateName: string;
    reportSubTitle: string;
    examineeInfoList: IExamineeInfo[];
}

type TAlignType = 'left' | 'center' | 'right';

export interface ITableColumn {
    field: string;
    label: string;
    width?: number | string;
    align?: TAlignType;
    headerWidth?: number | string;
    children?: ITableColumn[];
    rowSpan?: Record<string, any>;
}

type TStatusType = 'info' | 'success' | 'danger' | 'warning';

export interface ILevelConfig {
    color: string;
    backgroundColor: string;
    tagBackgroundColor: string;
    cardBackgroundColor: string;
}
