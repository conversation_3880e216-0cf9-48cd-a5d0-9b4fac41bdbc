<template>
    <div class="chart">
        <div class="chart__header">
            <span v-for="(item, index) in list" :key="item.dimensionId" class="chart__item-name" :style="getDimensionNameSpanStyle(index)">{{ item.showName }}</span>
        </div>
        <div class="chart__svg">
            <svg width="100%" height="100%" :viewBox="`0 0 ${CHART_X_WIDTH} ${CHART_Y_HEIGHT}`">
                <polyline :points="svgPoints" :stroke="LINE_COLOR" stroke-width="2" fill="none" />
                <circle v-for="item in svgCoordinateGroup" :key="item.x" :cx="item.x" :cy="item.y" r="3" :stroke="LINE_COLOR" stroke-width="1" :fill="CIRCLE_FILL_COLOR" />
                <line x1="12" y1="110" x2="690" y2="110" stroke="#eaeaea" stroke-width="2" />
                <text x="10" y="9" font-size="10" :fill="TEXT_FILL_COLOR" text-anchor="end">10</text>
                <text x="10" y="59" font-size="10" :fill="TEXT_FILL_COLOR" text-anchor="end">5</text>
                <text x="10" y="109" font-size="10" :fill="TEXT_FILL_COLOR" text-anchor="end">0</text>
            </svg>
        </div>
        <div class="chart__sub-item" ref="subItemRefs">
            <span
                v-for="subItem in subDimensionDetails"
                :key="subItem.dimensionName"
                ref="subLabelRefs"
                class="chart__sub-label"
                :style="{ width: `${getSubDimensionSpanWidth()}px` }"
                :data-item-index="subItem.parentIndex"
            >
                <span ref="subLabelTextRefs" class="chart__sub-label-text">{{ subItem.showName }}</span>
            </span>
        </div>
    </div>
</template>

<script setup lang="ts" name="LineChart">
import { computed, ref } from 'vue';
import type { IDimensionItem } from '../type/type';

interface IProps {
    list?: IDimensionItem[];
}

const CIRCLE_FILL_COLOR = '#ffffff';
const LINE_COLOR = '#00BEBD';
const TEXT_FILL_COLOR = '#aaaaaa';
const SPACE = 10;
const CHART_X_WIDTH = 670;
const CHART_Y_HEIGHT = 110;

const props = withDefaults(defineProps<IProps>(), {
    list: () => [],
});

const subDimensionDetails = computed(() => {
    return props.list.map((item, parentIndex) => item.subDimensionDetails.map((childItem) => ({ ...childItem, parentIndex }))).flat();
});

const subLabelRefs = ref<HTMLSpanElement[]>([]);
const subLabelTextRefs = ref<HTMLSpanElement[]>([]);
const subItemRefs = ref<HTMLDivElement>();

const svgCoordinateGroup = computed(() => {
    const subItemWidth = getSubDimensionSpanWidth();
    return subLabelRefs.value.map((item, index) => ({
        x: subItemWidth / 2 + subItemWidth * index,
        y: (SPACE - Number(subDimensionDetails.value[index].score)) * (CHART_Y_HEIGHT / SPACE),
    }));
});

const svgPoints = computed(() => {
    return svgCoordinateGroup.value.map((item) => `${item.x},${item.y}`).join(' ');
});

function getDimensionNameSpanStyle(index: number) {
    const length = subDimensionDetails.value?.filter((item) => item.parentIndex === index).length || 0;
    return {
        width: `${length * getSubDimensionSpanWidth()}px`,
    };
}

function getSubDimensionSpanWidth() {
    const length = subDimensionDetails.value.length;
    return CHART_X_WIDTH / length;
}
</script>

<style lang="less" scoped>
.chart {
    overflow: hidden;
    margin-top: var(--spacing-large);
    &__header {
        width: 100%;
        display: flex;
        padding-left: 10px;
        margin-bottom: var(--spacing-xlarge);
    }

    &__svg {
        width: 100%;
        height: 110px;
    }

    &__item-name {
        text-align: center;
        line-height: 20px;
        color: var(--fill-color-1);
        background: #b1b1b1;
        border-radius: 81px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        &:after {
            content: '';
            display: block;
            width: 1px;
            height: 300px;
            background: #eaeaea;
            position: absolute;
            top: 0;
            right: 0;
        }
        &:last-child {
            &:after {
                display: none;
            }
        }
    }

    &__sub-item {
        padding: 0 var(--spacing-normal);
        display: flex;
    }

    &__sub-label {
        width: 40px;
        font-size: 10px;
        line-height: 20px;
        text-align: center;
        display: inline-block;
        flex-shrink: 0;
    }
}
</style>
