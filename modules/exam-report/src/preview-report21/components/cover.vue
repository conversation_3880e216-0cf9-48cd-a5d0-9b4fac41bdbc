<template>
    <div class="cover-page report-preview__content--single-page">
        <div v-if="logo" class="cover-page__logo">
            <img :src="logo" alt="logo" />
        </div>
        <div v-else class="cover-page__logo-text">LOGO</div>
        <div class="cover-page__title">
            {{ data.templateName }}
        </div>
        <div class="cover-page__subtitle">
            {{ data.reportSubTitle }}
        </div>
        <div class="cover-page__info">
            <div class="cover-page__info-wrap">
                <div v-for="(item, index) of data.examineeInfoList" :key="index" class="cover-page__info-item">{{ item.fieldShowName }}：{{ item.fieldValue }}</div>
            </div>
        </div>
        <Copyright type="cover"></Copyright>
    </div>
    <div class="page-split"></div>
</template>

<script setup lang="ts" name="Cover">
import Copyright from '@modules/exam-report/components/copyright/index.vue';
import type { ICoverData } from '../type/type';

defineProps<{
    data: ICoverData;
    logo?: string;
}>();
</script>

<style lang="less" scoped>
.report-preview__content--single-page {
    --cover-page-title-font-size: 60px;
    --cover-page-title-line-height: 71px;
    --cover-page-padding-top: 161px;
    --cover-page-title-width: 608px;
    --cover-page-subtitle-width: 576px;
    --cover-page-divider-width: 183px;
    --cover-page-margin-left: calc(83px - var(--page-padding-horizontal));
}

.cover-page {
    width: 794px;
    height: 1124px;
    position: relative;
    background-size: contain;
    background-repeat: no-repeat;
    word-break: break-all;
    white-space: pre-wrap;
    background-image: url('https://img.bosszhipin.com/static/zhipin/kanjian/zhice/report/461548131057118404.svg');
    background-position: center bottom;
    margin: var(--spacing-none) auto;
    padding-top: var(--cover-page-padding-top);
    padding-left: var(--page-padding-horizontal);
    padding-right: var(--page-padding-horizontal);

    &__logo {
        position: absolute;
        right: 35px;
        top: 35px;
        width: 160px;
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;

        img {
            display: block;
            max-height: 100%;
            max-width: 100%;
        }
    }

    &__logo-text {
        position: absolute;
        right: 35px;
        top: 35px;
        width: 160px;
        height: 80px;
        line-height: 80px;
        background: var(--gray-color-3);
        color: var(--secondary-text-color);
        opacity: 0.2;
        font-size: 34px;
        text-align: center;
        font-weight: bold;
        visibility: hidden;
    }

    &__title {
        width: var(--cover-page-title-width);
        color: var(--main-body-text-color);
        font-family: var(--FZLanTingHeiS-DB-GB);
        font-size: var(--cover-page-title-font-size);
        line-height: var(--cover-page-title-line-height);
        text-align: left;
        padding: var(--spacing-none);
        margin-left: var(--cover-page-margin-left);
    }
    &__subtitle {
        width: var(--cover-page-subtitle-width);
        font-family: var(--FZLanTingHeiS-L-GB);
        padding: var(--spacing-none);
        margin-top: 7px;
        margin-left: var(--cover-page-margin-left);
        font-size: var(--font-size-title-5);
        color: var(--main-body-text-color);
        line-height: 32px;
        text-align: left;
        opacity: 0.7;
        word-break: normal;
        letter-spacing: 0.5px;
    }

    &__info {
        font-family: var(--FZLanTingHeiS-R-GB);
        color: var(--main-body-text-color);
        text-align: left;
        justify-content: flex-start;
        margin: 90px 0 0 calc(83px - var(--page-padding-horizontal));
        font-size: var(--font-size-body-4);
        line-height: var(--base-line-height-small);
    }
}
</style>
