<template>
    <div class="level-indicator">
        <span v-for="i in LEVEL_MAX" :key="i" :style="getStyle(i)"></span>
    </div>
</template>

<script setup lang="ts" name="LevelIndicator">
import { getBackgroundColor } from '../config';
import type { CSSProperties } from 'vue';

interface IProps {
    level?: number;
}

const LEVEL_MAX = 3;

// 定义组件属性
const props = withDefaults(defineProps<IProps>(), {
    level: 1,
});

const BASE_BG_COLOR = '#F2F2F2';

function getStyle(i: number): CSSProperties {
    // props.level 固定 1~3
    return {
        backgroundColor: i <= props.level ? getBackgroundColor(props.level) : BASE_BG_COLOR,
    };
}
</script>

<style lang="less" scoped>
.level-indicator {
    display: flex;
    gap: 2px;

    span {
        width: 12px;
        height: 12px;
        display: inline-block;
        background: #f2f2f2;
        transition: background-color 0.2s;

        &:first-child {
            border-radius: var(--report-border-radius-small) 0 0 var(--report-border-radius-small);
        }

        &:last-child {
            border-radius: 0 var(--report-border-radius-small) var(--report-border-radius-small) 0;
        }
    }
}
</style>
