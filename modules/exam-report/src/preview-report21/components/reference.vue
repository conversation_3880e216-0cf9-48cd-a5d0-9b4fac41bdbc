<template>
    <PageHeader />
    <SectionTitle title="附录" />
    <SubTitle>维度说明</SubTitle>
    <Table :tableData="tableData" :columns="dimensionListColumns" />
    <SubTitle class="fu-lu__usage-title">使用声明</SubTitle>
    <RichText v-if="data.usageInstructions" :domString="data.usageInstructions" :richTextIndex="334" />
    <PageFooter :data="pageFooterData" />
</template>

<script setup lang="ts" name="Reference">
import { computed } from 'vue';
import type { IData, IProps } from '../type/type';
import RichText from '@modules/exam-report/components/rich-text/index.vue';
import PageFooter from '@modules/exam-report/components/page-footer/index.vue';
import PageHeader from '@modules/exam-report/components/page-header/index.vue';
import SectionTitle from '@modules/exam-report/components/section-title/index.vue';
import SubTitle from '@modules/exam-report/components/sub-title/index.vue';
import Table from '@modules/exam-report/components/table/index.vue';
import { dimensionListColumns } from '../config';

const props = withDefaults(defineProps<IProps>(), {
    data: () => ({}) as IData,
    pageFooterData: () => [],
});

const tableData = computed(() => {
    return props.data?.dimensionDetails?.map((item) => {
        return { ...item, children: item.subDimensionDetails };
    });
});
</script>

<style lang="less" scoped>
.fu-lu {
    &__answer-info {
        font-family: var(--FZLanTingHeiS-M-GB);
        margin: var(--spacing-normal) 0 var(--spacing-xlarge);
    }

    &__answer-info-title {
        font-size: var(--font-size-body-3);
    }

    &__answer-info-desc {
        line-height: var(--base-line-height-normal);
        font-size: var(--font-size-body-2);
    }

    &__reference-table {
        margin-bottom: var(--spacing-normal);
    }

    &__reference-header {
        font-weight: var(--font-weight-4);
        padding: var(--spacing-normal) var(--spacing-normal) var(--spacing-normal) 30px;
    }

    &__usage-title {
        margin-top: var(--spacing-xlarge);
    }
}
</style>
