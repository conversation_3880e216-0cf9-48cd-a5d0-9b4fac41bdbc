<template>
    <PageHeader />
    <div class="comprehensive-result__indicative">
        <span class="comprehensive-result__indicative-name">{{ getExamineeName }}</span>
        <span class="comprehensive-result__indicative-item" v-for="(item, index) in data.indicativeList" :key="index">{{ item.showName }}：{{ item.result }}</span>
    </div>
    <SubTitle>政治素养水平</SubTitle>
    <div class="comprehensive-result__total">
        <div class="comprehensive-result__level">
            <div class="comprehensive-result__level-tag" :style="getBackgroundStyle(data.totalScoreLevel, 'tag')">
                {{ data.totalLevelStr }}
            </div>
            <div class="comprehensive-result__level-desc">
                <Progress
                    class="comprehensive-result__level-process"
                    :borderRadius="2"
                    :footerConfig="false"
                    :height="12"
                    :minPartWidth="19"
                    :partColors="['#F2F2F2']"
                    :showTick="true"
                    :splitArray="splitArray"
                    :totalWidth="'100%'"
                    :type="'grow'"
                    :value="data.totalScore"
                    :valueBarStyle="{ color: getBackgroundColor(data.totalScoreLevel) }"
                >
                    <template #value-indicator>
                        <div class="number" :style="{ '--bgc': getBackgroundColor(data.totalScoreLevel) }">{{ data.totalScore }}</div>
                    </template>
                </Progress>
                <div class="comprehensive-result__level-desc">{{ data.totalLevelDescription }}</div>
            </div>
        </div>
        <LineChart :list="data.dimensionDetails" />
    </div>

    <SubTitle>详细结果</SubTitle>
    <template v-for="item in data.dimensionDetails" :key="item.dimensionName">
        <div class="comprehensive-result__detail-result">
            <SubTitle type="title" size="medium" class="comprehensive-result__detail-result-title">
                {{ item.showName }}
                <div class="comprehensive-result__detail-result-score">{{ item.score }}</div>
                <LevelIndicator :level="item.level" />
                <div class="comprehensive-result__detail-result-desc">{{ item.definition }}</div>
            </SubTitle>
            <Card border="1px" class="comprehensive-result__card" :style="getBackgroundStyle(item.level, 'card')">
                <template v-for="subItem in item.subDimensionDetails" :key="subItem.dimensionName">
                    <div class="comprehensive-result__card-item">
                        <div class="comprehensive-result__card-item-header">
                            <div>{{ subItem.showName }}</div>
                            <div>{{ subItem.score }}</div>
                            <LevelIndicator :level="subItem.level" />
                        </div>
                        <div class="comprehensive-result__detail-result-sub-desc">
                            {{ subItem.levelDescription }}
                        </div>
                    </div>
                </template>
            </Card>
        </div>
    </template>

    <div>
        <SubTitle>面试建议</SubTitle>
        <template v-for="(item, index) in data.interviewSuggestions" :key="index">
            <Card border="1px" class="comprehensive-result__card" :style="getBackgroundStyle(item.level, 'card')">
                <div class="comprehensive-result__card-item-header">
                    <div>{{ item.showName }}</div>
                    <div>{{ item.score }}</div>
                    <LevelIndicator :level="item.level" />
                </div>
                <div class="comprehensive-result__card-item-main">
                    <div class="comprehensive-result__card-group"><span class="comprehensive-result__label">面试问题：</span>{{ item.interviewQuestions }}</div>
                    <div class="comprehensive-result__card-group">
                        <span class="comprehensive-result__label">考察要点：</span>
                        <PointList v-if="item.keyPoints" class="comprehensive-result__point-list" :data="getPointList(item.keyPoints)"></PointList>
                    </div>
                </div>
            </Card>
        </template>
    </div>
    <PageFooter :data="pageFooterData" />
</template>

<script setup lang="ts" name="ComprehensiveResult">
import { computed } from 'vue';
import type { IData, IProps, LevelEnum } from '../type/type';
import PageFooter from '@modules/exam-report/components/page-footer/index.vue';
import PageHeader from '@modules/exam-report/components/page-header/index.vue';
import SubTitle from '@modules/exam-report/components/sub-title/index.vue';
import Card from '@modules/exam-report/components/card/index.vue';
import PointList from '@modules/exam-report/components/point-list/index.vue';
import Progress from '@modules/exam-report/components/progress/index.vue';
import LevelIndicator from './level-indicator.vue';
import LineChart from './line-chart.vue';
import { getTagBackgroundColor, getCardBackgroundColor, getBackgroundColor } from '../config';

// 定义组件属性
const props = withDefaults(defineProps<IProps>(), {
    data: () => ({}) as IData,
    pageFooterData: () => [],
});

const getExamineeName = computed(() => {
    const name = props.data.examineeInfoList?.find((item) => item.fieldName === '姓名')?.fieldValue;
    const code = props.data.examineeInfoList?.find((item) => item.fieldName === '第三方唯一码')?.fieldValue;
    return name || code;
});

const splitArray = computed(() => {
    return [0, ...(props.data.levelArray || []), 10];
});

function getBackgroundStyle(level: LevelEnum, key: 'tag' | 'card') {
    const getBackgroundConfig = {
        tag: getTagBackgroundColor,
        card: getCardBackgroundColor,
    };
    return {
        background: getBackgroundConfig[key](level),
    };
}

function getPointList(keyPoints: string) {
    return keyPoints.split('\n');
}
</script>

<style lang="less" scoped>
.comprehensive-result {
    &__indicative {
        background: var(--gray-color-2);
        font-family: var(--FZLanTingHeiS-R-GB);
        border-radius: var(--report-border-radius-normal);
        display: flex;
        align-items: center;
        margin-bottom: var(--spacing-xlarge);
        padding: var(--spacing-medium) var(--spacing-large);
    }

    &__indicative-name {
        font-family: var(--FZLanTingHeiS-DB-GB);
        font-size: var(--report-base-font-size-medium);
        transform: translateY(2px);
        margin-right: var(--spacing-normal);
    }

    &__indicative-item {
        &:after {
            content: '';
            width: 1px;
            height: 14px;
            background-color: var(--gray-color-8);
            color: var(--secondary-text-color);
            display: inline-block;
            vertical-align: middle;
            margin: 0 var(--spacing-normal);
        }
        &:last-child {
            &:after {
                display: none;
            }
        }
    }

    &__total {
        margin-bottom: 50px;
    }

    &__level {
        display: flex;
        gap: 22px;
    }

    &__level-tag {
        width: 90px;
        height: 90px;
        line-height: 90px;
        border-radius: 10px;
        color: #ffffff;
        font-family: var(--FZLanTingHeiS-DB-GB);
        font-size: 22px;
        text-align: center;
        flex-shrink: 0;
    }

    &__level-process {
        margin-bottom: var(--spacing-normal);
    }

    &__level-desc {
        width: 578px;
    }

    &__detail-result {
        margin-bottom: var(--spacing-large);
    }

    &__detail-result-title {
        margin-bottom: var(--spacing-none);
        display: flex;
        align-items: center;
        gap: var(--spacing-normal);
    }

    &__detail-result-score {
        color: #000000;
        font-size: 20px;
        font-weight: bold;
    }

    &__detail-result-desc {
        font-family: var(--FZLanTingHeiS-R-GB);
        line-height: var(--report-base-line-height-normal);
        color: var(--text-color);
    }

    &__detail-result-sub-desc {
        font-size: var(--report-base-font-size-small);
        font-family: var(--FZLanTingHeiS-R-GB);
        line-height: var(--report-base-line-height-normal);
        color: var(--secondary-text-color);
        padding: var(--spacing-normal) 0;
        border-bottom-width: 1px;
        border-bottom-style: solid;
        border-bottom-color: var(--gray-color-2);
    }

    &__card {
        padding: var(--spacing-none) var(--spacing-normal);
        margin-bottom: var(--spacing-normal);
    }

    &__card-item {
        &:last-child {
            .comprehensive-result__detail-result-sub-desc {
                border-bottom-width: 0;
            }
        }
    }

    &__card-item-header {
        line-height: var(--report-base-line-height-normal);
        display: flex;
        align-items: center;
        gap: var(--spacing-normal);
        padding-top: var(--spacing-normal);
    }

    &__card-item-main {
        background: var(--fill-color-1);
        border-radius: 3px;
        margin-top: var(--spacing-normal);
        padding: var(--spacing-medium) var(--spacing-large);
    }

    &__card-group {
        display: flex;
        line-height: var(--report-base-line-height-normal);
    }

    &__label {
        width: 80px;
        display: inline-block;
    }

    &__point-list {
        color: var(--secondary-text-color);
        font-family: var(--FZLanTingHeiS-R-GB);
        font-size: var(--report-base-font-size-small);
        line-height: var(--report-base-line-height-normal);
        &:before {
            width: 2px;
            height: 2px;
        }
    }
}
:deep(.comprehensive-result__level-process) {
    .value-indicator-wrap {
        transform: translate(-50%, 0);
        top: calc(100% - 35px);
        white-space: nowrap;
        .number {
            color: #ffffff;
            font-family: var(--Kanzhun);
            font-size: 12px;
            line-height: 16px;
            height: 16px;
            padding: 0 8.58px;
            border-radius: 35px;
            background-color: var(--bgc);
            position: relative;
            &::after {
                content: '';
                position: absolute;
                top: 16px;
                left: 50%;
                transform: translate(-50%, 1%);
                width: 0;
                height: 0;
                border-left: 3px solid transparent;
                border-right: 3px solid transparent;
                border-top: 5.7px solid var(--bgc);
            }
        }
    }
}
</style>
