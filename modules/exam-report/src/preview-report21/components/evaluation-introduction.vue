<template>
    <PageHeader />
    <SectionTitle title="测评介绍" />
    <div class="evaluation-introduction__description">
        {{ data.testDescription }}
    </div>
    <div class="evaluation-introduction__dimension-describe">
        <img src="https://img.bosszhipin.com/static/file/2024/hs07lejlp11709288492820.png.webp" />
        <div v-for="item in data.dimensionDetails || []" :key="item.dimensionName" class="evaluation-introduction__dimension-box">
            <div class="evaluation-introduction__dimension-title">{{ item.dimensionName }}</div>
            <div class="evaluation-introduction__dimension-content">{{ item.definition }}</div>
        </div>
    </div>
    <PageFooter :data="pageFooterData" />
</template>

<script setup lang="ts" name="EvaluationIntroduction">
import type { IData, IProps } from '../type/type';
import PageFooter from '@modules/exam-report/components/page-footer/index.vue';
import PageHeader from '@modules/exam-report/components/page-header/index.vue';
import SectionTitle from '@modules/exam-report/components/section-title/index.vue';

withDefaults(defineProps<IProps>(), {
    data: () => ({}) as IData,
    pageFooterData: () => [],
});
</script>
<style lang="less" scoped>
.evaluation-introduction {
    &__description {
        line-height: var(--report-base-line-height-normal);
        margin-bottom: 32px;
    }

    &__dimension-describe {
        background-color: var(--gray-color-2);
        text-align: center;
        border-radius: var(--report-border-radius-huge);
        padding: 32px;

        img {
            width: 260px;
            margin-bottom: 32px;
        }
    }

    &__dimension-box {
        display: flex;
        gap: var(--spacing-medium);
        margin-bottom: var(--spacing-xlarge);
        &:last-child {
            margin-bottom: var(--spacing-none);
        }
    }

    &__dimension-title {
        width: 100px;
        font-family: var(--FZLanTingHeiS-DB-GB);
        font-size: var(--report-base-font-size-medium);
        line-height: var(--report-base-line-height-normal);
        text-align: left;
    }

    &__dimension-content {
        font-family: var(--FZLanTingHeiS-R-GB);
        font-size: var(--report-base-font-size-small);
        line-height: var(--report-base-line-height-normal);
        text-align: left;
    }
}
</style>
