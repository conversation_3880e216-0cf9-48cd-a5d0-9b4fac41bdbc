<template>
    <div v-if="data" class="report-preview report-preview--product-20 report-preview-page">
        <WaterMark :content="data.watermarkStr || '001-20240101-163'" :waterMarkConfig="waterMarkConfig">
            <Cover v-if="isShowCover" :data="data" :logo="effectiveParams.reportLogo" />
            <div class="report-preview__content report-preview__content--single-page">
                <template v-for="item of componentsRender" :key="item.moduleCode">
                    <component
                        :is="item.component"
                        :data="data"
                        :pageFooterData="pageFooterData"
                        :moduleCodes="moduleCodes.filter((x) => x !== ModuleEnum.Cover)"
                        :headerFooterConfig="headerFooterConfig"
                    />
                </template>
            </div>
        </WaterMark>
    </div>
</template>

<script setup lang="ts">
import { computed, nextTick, onMounted, ref, watch } from 'vue';
import Cover from './components/cover.vue';
import ReportDescription from './components/report-description.vue';
import ComprehensiveResult from './components/comprehensive-result.vue';
import Reference from './components/reference.vue';
import WaterMark from '@modules/exam-report/components/watermark/index.vue';
import { debounce } from '@modules/exam-report/utils/index';
import { ModuleEnum } from './type/type';

defineOptions({ name: 'ReportPreview', inheritAttrs: false });

const props = defineProps({
    productId: {
        type: Number,
        default: 1,
    },
    params: {
        type: Object,
        default: () => ({}),
    },
    getDataFunction: {
        type: Function,
        default: () => {},
    },
    requestParams: {
        type: Object,
    },
    reportModuleCodes: {
        type: String,
        default: JSON.stringify([ModuleEnum.Cover, ModuleEnum.ReportDescription, ModuleEnum.ComprehensiveResult, ModuleEnum.Reference]),
    },
});

const headerFooterConfig: Record<string, boolean> = {
    header: true,
    footer: true,
};

const pageFooterData = computed(() => {
    const list = ['姓名', '政治素养测评报告', '报告参考性'];
    const examineeInfoList = data.value?.examineeInfoList || [];

    return list.map((item) => {
        const itemData = examineeInfoList.find((x: { fieldName: string }) => x.fieldName === item);
        if (item === '姓名') {
            if (itemData) {
                return itemData?.fieldValue;
            }
            const row = examineeInfoList.find((x: { fieldName: string }) => x.fieldName === '第三方唯一码');
            return row?.fieldValue || item;
        }
        if (item === '报告参考性' && itemData) {
            return `${itemData?.fieldShowName || ''}：${itemData?.fieldValue || ''}`;
        }
        if (item === '政治素养测评报告') return '政治素养测评报告';
        return itemData?.fieldValue || '';
    });
});

const effectiveParams = computed(() => {
    const { reportExamineeField, reportDimensionField, reportTitle, extraParam, reportLogo } = props.params;
    return {
        reportTitle,
        reportSubTitle: extraParam?.templateSubTitle,
        reportLogo: reportLogo || data.value?.reportLogo,
        reportType: extraParam?.reportType,
        reportExamineeField: JSON.stringify(reportExamineeField?.map((x: { code: string }) => x.code)),
        reportDimensionField: JSON.stringify(
            reportDimensionField?.map((x: { code: string; showName: string; dimName: string }) => ({ code: x.code, showName: x.showName || x.dimName }))
        ),
    };
});

const waterMarkConfig = computed(() => {
    if (props.requestParams) return 2;
    if (props.params?.watermark) return props.params.watermark;
    return data.value?.watermark;
});

const isShowCover = computed(() => {
    return moduleCodes.value.includes(ModuleEnum.Cover);
});

const moduleCodes = ref<string[]>([]);

const ComponentsAll = [
    { moduleCode: ModuleEnum.Cover, component: Cover },
    { moduleCode: ModuleEnum.ReportDescription, component: ReportDescription },
    { moduleCode: ModuleEnum.ComprehensiveResult, component: ComprehensiveResult },
    { moduleCode: ModuleEnum.Reference, component: Reference },
];
const componentsRender = ref<any[]>([]);
const data = ref();
let originReportName = '';
let originReportSubTitle = '';

async function getDetail() {
    const params = props.requestParams
        ? props.requestParams
        : {
              reportExaminees: effectiveParams.value.reportExamineeField,
              reportModules: effectiveParams.value.reportDimensionField,
              reportType: effectiveParams.value.reportType,
          };
    const res = await props.getDataFunction(params, props.productId);
    if (res.code === 0) {
        data.value = undefined;
        await nextTick();
        data.value = res.data || {};
        originReportName = data.value.templateName;
        originReportSubTitle = data.value.reportSubTitle;
        data.value.templateName = effectiveParams.value.reportTitle || originReportName;
        data.value.reportSubTitle = effectiveParams.value.reportSubTitle || originReportSubTitle;
    }
}

function reLayout() {
    setTimeout(() => {
        doLayout();
        doLayout2();
    }, 0);
}

onMounted(async () => {
    await getDetail();
    reLayout();
});
/**
 *
 * @param element 元素
 * @param {object} config - 其他配置
 * @param {boolean} config.ignoreAttendantAttribute - 是否忽略随从属性，要得到真实高度的话，需要设为 true
 */
function calcHeight(element: HTMLElement, config = { ignoreAttendantAttribute: false }) {
    if (element === undefined || (!config.ignoreAttendantAttribute && element?.dataset.attendantsBelongTo)) {
        return 0;
    }
    const height = element.offsetHeight + Number.parseFloat(getComputedStyle(element).marginTop) + Number.parseFloat(getComputedStyle(element).marginBottom);
    return height;
}
function doLayout() {
    const pageTotalHeight = 1124;
    const pageHeaderHeight = 67;
    const pageFooterHeight = 67;
    const avaliableHeight = pageTotalHeight - pageHeaderHeight - pageFooterHeight;
    const pageSplitDom = document.createElement('div');
    pageSplitDom.classList.add('page-split');
    const pageHeaderDom = document.querySelector('.report-preview__header');
    const pageFooterDom = document.querySelector('.report-preview__footer');

    let tempHeightSum = 0;
    const atoms = document.querySelectorAll('.report-preview__content--single-page.report-preview__content > *:not(.report-preview__header):not(.report-preview__footer)') || [];
    for (let i = 0; i < atoms.length; i++) {
        const atom = atoms[i];
        if (atom?.dataset?.hasAttendants) {
            const attendantsDoms = document.querySelectorAll(`[data-attendants-belong-to="${atom.dataset.hasAttendants}"]`);
            for (let j = 0; j < attendantsDoms.length; j++) {
                const attendantDom = attendantsDoms[j];
                tempHeightSum += calcHeight(attendantDom, { ignoreAttendantAttribute: true });
            }
        }
        const atomHeight = calcHeight(atom);
        const nextAtomHeight = calcHeight(atoms[i + 1]);
        tempHeightSum += atomHeight;
        const condition1 = atom.nextElementSibling?.classList.contains('report-preview__footer'); // 下个元素是页脚
        const condition2 = tempHeightSum + nextAtomHeight > avaliableHeight; // 剩余可用高度不足以放下下一个元素

        if (atoms[i + 1]?.dataset?.tableRow && condition2) {
            const tableHeaderDom = document.querySelector(`[data-table-header="${atoms[i + 1]?.dataset?.tableRow}"]`);
            let insertPosition = 'beforebegin';
            if (condition2) {
                insertPosition = 'afterend';
            }
            atom.insertAdjacentHTML(insertPosition, tableHeaderDom?.outerHTML);
            atoms.splice(i + 1, 0, tableHeaderDom);
        }

        if (condition1 || condition2) {
            tempHeightSum = 0;
            if (!condition1) {
                atom.insertAdjacentHTML('afterend', pageHeaderDom.outerHTML);
                atom.insertAdjacentHTML('afterend', pageFooterDom.outerHTML);
            }
        }
    }
    const allPageFooters = document.querySelectorAll('.report-preview__footer') || [];
    for (let i = 0; i < allPageFooters.length; i++) {
        // 更改页脚的页码
        const element = allPageFooters[i];
        element.querySelector('.report-preview__footer-count').innerText = i + 1;
    }
}
function doLayout2() {
    const pageSplitDom = document.createElement('div');
    pageSplitDom.classList.add('page-split');
    const divs = [];
    const wrapper = document.querySelector('.report-preview__content--single-page.report-preview__content');
    const atoms = document.querySelectorAll('.report-preview__content--single-page.report-preview__content > *') || [];
    for (let i = 0; i < atoms.length; i++) {
        const atom = atoms[i];
        if (atom.classList.contains('report-preview__header')) {
            divs[divs.length] = document.createElement('div');
        }
        divs[divs.length - 1].appendChild(atom);
    }
    for (let i = 0; i < divs.length; i++) {
        const div = divs[i];
        div.style.height = '1124px';
        div.dataset.isPageWrapper = 'true';
        if (i === divs.length - 1) {
            div.style.height = '1117px';
        }
        div.style.position = 'relative';
        const footer = div.querySelector('.report-preview__footer') || {};
        if (footer && footer?.style) {
            footer.style.position = 'absolute';
            footer.style.bottom = 0;
            footer.style.marginTop = 0;
        }
        wrapper.appendChild(div);
        div.insertAdjacentHTML('beforebegin', pageSplitDom.outerHTML);

        const bgContainer = document.createElement('div');
        bgContainer.classList.add('report-preview__bg-container');
        div.insertAdjacentElement('afterbegin', bgContainer);
    }
}

watch(
    () => effectiveParams.value.reportTitle,
    () => {
        data.value.templateName = effectiveParams.value.reportTitle || originReportName;
        const doms = document.querySelectorAll('.report-preview__footer .report-preview__footer-name') || [];
        for (let i = 0; i < doms.length; i++) {
            const el = doms[i];
            el.innerText = data.value.templateName;
        }
    }
);

watch(
    () => effectiveParams.value.reportSubTitle,
    () => {
        data.value.reportSubTitle = effectiveParams.value.reportSubTitle || originReportSubTitle;
    }
);

watch(
    [() => effectiveParams.value.reportExamineeField, () => effectiveParams.value.reportDimensionField],
    debounce(async () => {
        await getDetail();
        reLayout();
    }, 500)
);

watch(
    () => props.reportModuleCodes,
    debounce(async () => {
        moduleCodes.value = JSON.parse(props.reportModuleCodes).map((x: any) => String(x));
        const localModuleCodes = moduleCodes.value.filter((x) => x !== '1');
        componentsRender.value = ComponentsAll.filter((component) => localModuleCodes.includes((component.moduleCode?.split('-') || [])[0]));
        const tempData = data.value;
        data.value = undefined;
        await nextTick();
        data.value = tempData;
        reLayout();
    }, 500),
    { immediate: true }
);
</script>

<style lang="less" scoped>
.report-preview {
    &__header {
        margin-bottom: 0;
        position: absolute;
    }
    &__content {
        width: 794px;
        background-color: var(--base-background-color);
        margin: 0 auto;
        padding-left: var(--page-padding-horizontal);
        padding-right: var(--page-padding-horizontal);
        position: relative;
    }
}
</style>
