import ReportPreview1 from './preview-report1/index.vue';
import ReportPreview2 from './preview-report2/index.vue';
import ReportPreview3 from './preview-report3/index.vue';
import ReportPreview4 from './preview-report4/index.vue';
import ReportPreview5 from './preview-report5/index.vue';
import ReportPreview6 from './preview-report6/index.vue';
import ReportPreview7 from './preview-report7/index.vue';
import ReportPreview7_1 from './preview-report7_1/index.vue';
import ReportPreview8 from './preview-report8/index.vue';
import ReportPreview9 from './preview-report9/index.vue';
import ReportPreview9_1 from './preview-report9_1/index.vue';
import ReportPreview10 from './preview-report10/index.vue';
import ReportPreview11 from './preview-report11/index.vue';
import ReportPreview12 from './preview-report12/index.vue';
import ReportPreview13 from './preview-report13/index.vue';
import ReportPreview13_1 from './preview-report13_1/index.vue';
import ReportPreview13_2 from './preview-report13_2/index.vue';
import ReportPreview14 from './preview-report14/index.vue';
import ReportPreview15 from './preview-report15/index.vue';
import ReportPreview16 from './preview-report16/index.vue';
import ReportPreview17 from './preview-report17/index.vue';
import ReportPreview18 from './preview-report18/index.vue';
import ReportPreview19 from './preview-report19/index.vue';
import ReportPreview20 from './preview-report20/index.vue';
import ReportPreview21 from './preview-report21/index.vue';

// 公共资源
const PUBLIC_RESOURSE = {
    font: [],
    img: [],
} as const;

// 私有资源
const PROPRIETARY_RESOURSE = [
    {
        productId: 100,
        productName: '校招',
        resource: {
            font: [
                {
                    name: 'MiSans',
                    // url_server: 'https://************:3000/kanjian-assets/zhizhao-report/MiSans-Semibold.ttf',
                    // url_office: 'https://g.weizhipin.com/kanjian-assets/zhizhao-report/MiSans-Semibold.ttf',
                    url_public: 'https://img.bosszhipin.com/static/file/2024/MiSans.ttf',
                },
                {
                    name: 'FZLanTingHeiS-EB-GB',
                    // url_server: 'https://************:3000/kanjian-assets/zhizhao-report/MiSans-Semibold.ttf',
                    // url_office: 'https://g.weizhipin.com/kanjian-assets/zhizhao-report/MiSans-Semibold.ttf',
                    url_public: 'https://img.bosszhipin.com/static/file/2024/FZLanTingHeiS-EB-GB.TTF',
                },
                {
                    name: 'FZLanTingHeiS-DB-GB',
                    // url_server: 'https://************:3000/kanjian-assets/zhizhao-report/MiSans-Semibold.ttf',
                    // url_office: 'https://g.weizhipin.com/kanjian-assets/zhizhao-report/MiSans-Semibold.ttf',
                    url_public: 'https://img.bosszhipin.com/static/file/2024/FZLanTingHeis-DB-GB.TTF',
                },
                {
                    name: 'FZLanTingHeiS-R-GB',
                    // url_server: 'https://************:3000/kanjian-assets/zhizhao-report/MiSans-Semibold.ttf',
                    // url_office: 'https://g.weizhipin.com/kanjian-assets/zhizhao-report/MiSans-Semibold.ttf',
                    url_public: 'https://img.bosszhipin.com/static/file/2024/FzLanTingHeiS-R-GB.TTF',
                },
            ],
        },
    },
];
const FINAL_RESOURSE = PROPRIETARY_RESOURSE.map((x) => {
    for (const resourceKey of Object.keys(PUBLIC_RESOURSE)) {
        if (!x.resource[resourceKey]) {
            x.resource[resourceKey] = [];
        }
        x.resource[resourceKey] = [...PUBLIC_RESOURSE[resourceKey], ...x.resource[resourceKey]];
    }
    return x;
});

export default {
    RESOURSE: FINAL_RESOURSE,
    ReportPreview1,
    ReportPreview2,
    ReportPreview3,
    ReportPreview4,
    ReportPreview5,
    ReportPreview6,
    ReportPreview7,
    ReportPreview7_1,
    ReportPreview8,
    ReportPreview9,
    ReportPreview9_1,
    ReportPreview10,
    ReportPreview11,
    ReportPreview12,
    ReportPreview13,
    ReportPreview13_1,
    ReportPreview13_2,
    ReportPreview14,
    ReportPreview15,
    ReportPreview16,
    ReportPreview17,
    ReportPreview18,
    ReportPreview19,
    ReportPreview20,
    ReportPreview21,
};

// 异步组件写法：只有真正使用到时，才会触发网络请求/编译
// export const ReportPreview1 = defineAsyncComponent(() =>
//     import('@modules/exam-report/preview-report1/index.vue'),
// )
// export const ReportPreview2 = defineAsyncComponent(() =>
//     import('@modules/exam-report/preview-report2/index.vue'),
// )
// export const ReportPreview3 = defineAsyncComponent(() =>
//     import('@modules/exam-report/preview-report3/index.vue'),
// )
// export const ReportPreview4 = defineAsyncComponent(() =>
//     import('@modules/exam-report/preview-report4/index.vue'),
// )
// export const ReportPreview5 = defineAsyncComponent(() =>
//     import('@modules/exam-report/preview-report5/index.vue'),
// )
// export const ReportPreview6 = defineAsyncComponent(() =>
//     import('@modules/exam-report/preview-report6/index.vue'),
// )
// export const ReportPreview7 = defineAsyncComponent(() =>
//     import('@modules/exam-report/preview-report7/index.vue'),
// )
// export const ReportPreview7_1 = defineAsyncComponent(() =>
//     import('@modules/exam-report/preview-report7_1/index.vue'),
// )
// export const ReportPreview8 = defineAsyncComponent(() =>
//     import('@modules/exam-report/preview-report8/index.vue'),
// )
// export const ReportPreview9 = defineAsyncComponent(() =>
//     import('@modules/exam-report/preview-report9/index.vue'),
// )
// export const ReportPreview9_1 = defineAsyncComponent(() =>
//     import('@modules/exam-report/preview-report9_1/index.vue'),
// )
// export const ReportPreview10 = defineAsyncComponent(() =>
//     import('@modules/exam-report/preview-report10/index.vue'),
// )
// export const ReportPreview11 = defineAsyncComponent(() =>
//     import('@modules/exam-report/preview-report11/index.vue'),
// )
// export const ReportPreview12 = defineAsyncComponent(() =>
//     import('@modules/exam-report/preview-report12/index.vue'),
// )
// export const ReportPreview13 = defineAsyncComponent(() =>
//     import('@modules/exam-report/preview-report13/index.vue'),
// )
// export const ReportPreview13_1 = defineAsyncComponent(() =>
//     import('@modules/exam-report/preview-report13_1/index.vue'),
// )
// export const ReportPreview13_2 = defineAsyncComponent(() =>
//     import('@modules/exam-report/preview-report13_2/index.vue'),
// )
// export const ReportPreview14 = defineAsyncComponent(() =>
//     import('@modules/exam-report/preview-report14/index.vue'),
// )
// export const ReportPreview15 = defineAsyncComponent(() =>
//     import('@modules/exam-report/preview-report15/index.vue'),
// )
// export const ReportPreview16 = defineAsyncComponent(() =>
//     import('@modules/exam-report/preview-report16/index.vue'),
// )
// export const ReportPreviewXiaoZhao1 = defineAsyncComponent(() =>
//     import('@modules/exam-report/preview-report-xiaozhao1/index.vue'),
// )

// export const RESOURSE = FINAL_RESOURSE
