:root {
    --tag-warning-color-1: #DE952F;
    --tag-warning-color-2: #E7A13E;
    /* 后期扩展预留位 */
    // --warning-color-3: '';
    --tag-warning-color-4: #E2D3C5;
    --tag-warning-color-5: #FFF5E7;
    --tag-warning-color-6: #FCFAF4;

    --tag-success-color-1: #00A6A7;
    --tag-success-color-2: #00BEBD;
    --tag-success-color-3: #49C7C7;
    // --tag-success-color-4: '';
    /* 后期扩展预留位 */
    --tag-success-color-5: #E4F9F9;
    // --tag-success-color-6: '';

    --tag-info-color-1: #0092FA;
    --tag-info-color-2: #00A3FA;
    /* 后期扩展预留位 */
    // --tag-info-color-3: '';
    --tag-info-color-4: #C5DBE2;
    --tag-info-color-5: #E8F7FF;
    --tag-info-color-6: #EEF5FB;

    --tag-danger-color-1: #F24955;
    // --tag-danger-color-2: '';
    --tag-danger-color-3: #E97570;
    /* 后期扩展预留位 */
    // --tag-danger-color-4: '';
    --tag-danger-color-5: #FFF0F1;


    // --base-line-height-xsmall: 22px;
    // --base-line-height-small: 24px;
    // --base-line-height-normal: 26px;

    --report-base-line-height-xsmall: 22px;
    --report-base-line-height-small: 24px;
    --report-base-line-height-normal: 26px;

    --report-base-font-size-small: 12px;
    --report-base-font-size-normal: 13px;
    --report-base-font-size-medium: 15px;
    --report-base-font-size-large: 18px;
    --report-base-font-size-xlarge: 28px;

     /* 边框圆角 */
    --report-border-radius-none: 0;
    --report-border-radius-small: 2px;
    --report-border-radius-normal: 3px;
    --report-border-radius-medium: 4px;
    --report-border-radius-large: 5px;
    --report-border-radius-xlarge: 6px;
    --report-border-radius-huge: 8px;
    --report-border-radius-circle: 9999px;

    --report-border-width-none: 0;
    --report-border-width-small: 1px;
    --report-border-width-normal: 2px;
    --report-border-width-medium: 3px;
    --report-border-width-large: 4px;
    --report-border-width-xlarge: 5px;

     /* 边框圆角 */
    // --border-radius-none: 0px;
    // --border-radius-small: 2px;
    // --border-radius-normal: 3px;
    // --border-radius-medium: 4px;
    // --border-radius-large: 5px;
    // --border-radius-xlarge: 6px;
    // --border-radius-huge: 8px;
    // --border-radius-circle: 9999px;

    --spacing-none: 0px;
    --spacing-small: 5px;
    --spacing-normal: 10px;
    --spacing-medium: 15px;
    --spacing-large: 20px;
    --spacing-xlarge: 22px;

    /* 次要的辅助性文本颜色，如说明、页脚等文本，颜色较浅 */
    --secondary-text-color: var(--gray-color-6);
    /* 正文文本颜色，用于页面主要的文本部分 1f1f1f */
    --main-body-text-color: var(--gray-color-8);

    /* 标题文本颜色，用于页面标题部分 */
    --title-text-color: var(--gray-color-8);

    // 字体
    --font-family-fz-jun-xi:
        'FZJun-XHJ', inter, '-apple-system', blinkmacsystemfont, 'PingFang SC', 'Hiragino Sans GB', 'noto sans',
        'Microsoft YaHei', 'Helvetica Neue', helvetica, arial, sans-serif;
    --font-family-fz-lt-xi:
        'FZLT-XHJ', inter, '-apple-system', blinkmacsystemfont, 'PingFang SC', 'Hiragino Sans GB', 'noto sans',
        'Microsoft YaHei', 'Helvetica Neue', helvetica, arial, sans-serif;
    --title-main-font-family:
        'FZLT-CHJ', inter, '-apple-system', blinkmacsystemfont, 'PingFang SC', 'Hiragino Sans GB', 'noto sans',
        'Microsoft YaHei', 'Helvetica Neue', helvetica, arial, sans-serif;
    --title-sub-font-family:
        'FZLT-ZHJ', inter, '-apple-system', blinkmacsystemfont, 'PingFang SC', 'Hiragino Sans GB', 'noto sans',
        'Microsoft YaHei', 'Helvetica Neue', helvetica, arial, sans-serif;
    --base-font-family:
        'FZLT-HJ', inter, '-apple-system', blinkmacsystemfont, 'PingFang SC', 'Hiragino Sans GB', 'noto sans',
        'Microsoft YaHei', 'Helvetica Neue', helvetica, arial, sans-serif;
    --number-font-family:
        'kanzhun', inter, '-apple-system', blinkmacsystemfont, 'PingFang SC', 'Hiragino Sans GB', 'noto sans',
        'Microsoft YaHei', 'Helvetica Neue', helvetica, arial, sans-serif;

    // motiff 字体重命名

    // 方正骏黑细
    --FZJunHeiS-L-GB: var(--font-family-fz-jun-xi);

    // 方正兰亭--细黑简体
    --FZLanTingHeiS-L-GB: var(--font-family-fz-lt-xi);

    // 方正兰亭--粗黑简体
    --FZLanTingHeiS-B-GB: var(--title-main-font-family);

    // 方正兰亭--中黑简体
    --FZLanTingHeiS-DB-GB: var(--title-sub-font-family);

    // 方正兰亭--黑简体
    --FZLanTingHeiS-R-GB: var(--base-font-family);

    // 看准数字
    --Kanzhun:
        'kanzhun', inter, '-apple-system', blinkmacsystemfont, 'PingFang SC', 'Hiragino Sans GB', 'noto sans',
        'Microsoft YaHei', 'Helvetica Neue', helvetica, arial, sans-serif;

    // 方正兰亭--纤黑简体
    --FZLanTingHeiS-EL-GB:
        'FZLT-XianHJ', inter, '-apple-system', blinkmacsystemfont, 'PingFang SC', 'Hiragino Sans GB', 'noto sans',
        'Microsoft YaHei', 'Helvetica Neue', helvetica, arial, sans-serif;

    // // Barlow Condensed 英文加粗
    --Barlow-Condensed-ExtraBold:
        'Barlow Condensed ExtraBold', inter, '-apple-system', blinkmacsystemfont, 'PingFang SC', 'Hiragino Sans GB',
        'noto sans', 'Microsoft YaHei', 'Helvetica Neue', helvetica, arial, sans-serif;

    // Barlow Condensed 英文加粗斜体
    --Barlow-Condensed-ExtraBold-Italic:
        'Barlow Condensed ExtraBold Italic', inter, '-apple-system', blinkmacsystemfont, 'PingFang SC',
        'Hiragino Sans GB', 'noto sans', 'Microsoft YaHei', 'Helvetica Neue', helvetica, arial, sans-serif;
}
