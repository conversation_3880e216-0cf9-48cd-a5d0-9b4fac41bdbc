import type { AssessmentResult } from '@/services/api/report/interface';
import type { AsyncComponentLoader, Component, ComputedRef, DeepReadonly, Raw, Ref } from 'vue';
import type { ReportModulesKey, ReportTemplate } from './shared';
import { get, post } from '@/services/http';
import { Skeleton } from '@boss/design';
import { PCode } from '@crm/biz-exam-product';
import { computed, defineAsyncComponent, h, markRaw, readonly } from 'vue';
import { reportModulesMap } from './shared';

import '@boss/design/es/skeleton/style.js';

export interface UseAsyncComponentOptions {
    /**
     * 加载中的组件，默认 Skeleton
     */
    loadingComponent?: Component;
    /**
     * 加载失败的组件，默认显示 '加载失败，请刷新重试'
     */
    errorComponent?: Component;
    /**
     * 超时时间，默认 60000
     */
    timeout?: number;
    /**
     * 延迟加载时间，默认 0
     */
    delay?: number;
}

/**
 * 使用异步组件加载器来加载组件，并配置加载过程中的加载、错误和超时行为
 * @param {AsyncComponentLoader<Component>} loader - 异步组件加载器，用于加载组件
 * @param {UseAsyncComponentOptions} options - 配置选项，包括加载组件、错误组件、超时时间和延迟时间
 * @return {Raw<Component>} - 标记为原始的（以防止 Vue 对其进行代理），通过异步加载得到的组件实例
 */
export function useAsyncComponent(loader: AsyncComponentLoader<Component>, options?: UseAsyncComponentOptions): Raw<Component> {
    return markRaw(
        defineAsyncComponent({
            loader,
            loadingComponent: options?.loadingComponent || (() => h(Skeleton, { rows: 5, animated: true })),
            errorComponent: options?.errorComponent || (() => h('div', {}, '加载失败，请刷新重试')),
            // 如果提供了一个 timeout 时间限制，并超时了
            // 也会显示这里配置的报错组件，默认值是：Infinity
            timeout: options?.timeout || 60000,
            delay: options?.delay || 0,
        })
    );
}

function buildInterface<T extends PCode>(id: number) {
    const basePath = `/wapi/admin/evaluation/report/template/${id}`;
    return {
        detail: (encryptId: string) => get<AssessmentResult<T> | null>(`${basePath}/info.json`, { encryptId }),
        save: (params: AssessmentResult<T>) => post<undefined>(`${basePath}/save.json`, params),
        previewSave: (params: AssessmentResult<T>) => post<undefined>(`${basePath}/preview/save.json`, params),
    };
}

/**
 * 报告模板配置数据。
 */
const reportTemplates: { id: PCode; encryptId: string; name: string }[] = [
    { id: PCode.CA, encryptId: '7d133318365793281g~~', name: '胜任力测评' },
    { id: PCode.HMA, encryptId: '12a764bdadbd106b1Q~~', name: '职业心理健康筛查测评' },
    { id: PCode.OEA, encryptId: 'f2b441dc736f8db41A~~', name: '五维性格测评' },
    { id: PCode.CRA, encryptId: 'afbd12e9b240e80a0w~~', name: '职业韧性测评' },
    { id: PCode.LPA, encryptId: '03e3d0a70f131b160g~~', name: '领导力情景测评' },
    { id: PCode.FCA, encryptId: 'e15629060159c6620Q~~', name: '金融人才胜任力测评' },
    { id: PCode.PTA, encryptId: '289c9e13f71d0c890A~~', name: '16T职业性格测评' },
    { id: PCode.OEWA, encryptId: 'f97af3d607584b413w~~', name: '五维性格测评W版' },
    { id: PCode.OMHA, encryptId: '5420b304e3e27fdf3g~~', name: '职业心理健康测评' },
    { id: PCode.HOTS, encryptId: 'cad865bee0a11ca41nQ~', name: '高阶思维能力测评' },
    { id: PCode.DISC, encryptId: 'bf0aaab5053fa3e71nU~', name: '工作风格测评' },
    { id: PCode.HIPO, encryptId: 'ce0adbff97a2b6361nY~', name: '高潜人才识别测评' },
    { id: PCode.GBA, encryptId: '78dfe43804a47d8a1nc~', name: 'GBA立体人才画像' },
    { id: PCode.ES, encryptId: 'e6da80e23dd5af9d1nA~', name: '敬业度调研' },
    { id: PCode.GPA, encryptId: '98314a46ad8da8901nE~', name: '笔迹职业适应力测评' },
    { id: PCode.CAA, encryptId: 'a0f71eca7c27840d1nI~', name: '认知能力测评' },
    { id: PCode.MA, encryptId: '609d0bf04eaf1b0f1nM~', name: '职业动机测评' },
    { id: PCode.SRC, encryptId: '0bedc3c1a2e139d01nw~', name: '校招方案组合' },
    { id: PCode.NTA, encryptId: 'd9cbdd3b551dc1421n0~', name: '新质人才测评方案' },
    { id: PCode.PQA, encryptId: '7197be0f338db9cd1XQ~', name: '政治素养测评' },
    { id: PCode.OEWA2, encryptId: 'b5799fd5b0d1ce3b1XU~', name: '五维性格2.0' },
];

/**
 * 报告模板列表。
 */
export const ReportTemplateList: DeepReadonly<ReportTemplate<any>[]> = readonly(
    reportTemplates.map((template) => ({
        ...template,
        code: template.id,
        interface: buildInterface(template.id),
    }))
);

export interface ModuleItem {
    id: string;
    label: string;
    component: ReturnType<typeof useAsyncComponent>;
}

/**
 * 通用模块组件生成函数
 *
 * @param code - PCode 类型的报告代码
 * @param moduleKeys - 模块键列表
 * @param getLabel - 获取模块标签的函数
 * @returns 返回一个包含模块组件的数组，每个组件都是一个异步组件。
 */
export function useDynamicModules(code: PCode, moduleKeys: Ref<string[]>, getLabel: (key: string) => string): ComputedRef<ModuleItem[]> {
    return computed(() => {
        return moduleKeys.value.map((moduleKey) => ({
            id: moduleKey,
            label: getLabel(moduleKey),
            component: useAsyncComponent(() => import(`./modules/${code}/${moduleKey}.vue`)),
        }));
    });
}

/**
 * 根据报告模块键生成报告模块组件列表。
 *
 * @param code - PCode 类型的报告代码，决定了需要加载哪些报告模块。
 * @param key - 包含报告模块键的响应式引用。
 * @returns 返回一个包含报告模块组件的数组，每个组件都是一个异步组件。
 */
export function useReportModules(code: PCode, key: Ref<ReportModulesKey[]>) {
    // 将 ReportModulesKey[] 转换为 string[]
    const moduleKeys = computed(() => key.value);

    // 定义如何根据模块键获取标签
    const getLabel = (moduleKey: string): string => {
        const entry = reportModulesMap.find(([key]) => key === moduleKey);
        return entry ? entry[1] : '';
    };

    return useDynamicModules(code, moduleKeys, getLabel);
}

/**
 * 根据模块配置生成模块组件列表。
 *
 * @param code - PCode 类型的报告代码，决定了需要加载哪些模块。
 * @param moduleConfigs - 包含模块键和标签的响应式引用。
 * @returns 返回一个包含模块组件的数组，每个组件都是一个异步组件。
 */
export function useModules(code: PCode, moduleConfigs: Ref<Record<string, string>>) {
    // 提取模块键列表
    const moduleKeys = computed(() => Object.keys(moduleConfigs.value));

    // 定义如何根据模块键获取标签
    const getLabel = (moduleKey: string): string => {
        return moduleConfigs.value[moduleKey] || '';
    };

    return useDynamicModules(code, moduleKeys, getLabel);
}

export function useReportComponent(code: PCode) {
    return useAsyncComponent(() => import(`./modules/${code}/index.vue`));
}
