<template>
    <component
        :is="currentProduct.component"
        v-if="currentProduct.component && fontLoaded"
        :get-data-function="currentProduct.getDataFunctionGroup[type]"
        :requestParams="{ encryptId, productId: currentProduct.code, ...currentProduct?.extraParam }"
    />
</template>

<script setup lang="ts">
import ReportService from '@/services/api/report/index';
import { PCode } from '@crm/biz-exam-product';
import ReportUi from '@kanjian/report-preview';
import { computed, onMounted, onUnmounted, ref } from 'vue';
import { useRoute } from 'vue-router';
import WebFont from 'webfontloader';
import { ReportType } from './action/shared';

// Moved font loading outside setup for better caching/reuse if this component is used multiple times
const fontLoadPromise = new Promise<void>((resolve, reject) => {
    WebFont.load({
        timeout: 60 * 1000,
        custom: {
            families: ['FZLT-CHJ', 'FZLT-ZHJ', 'FZLT-HJ', 'FZLT-XHJ', 'FZJun-XHJ', 'kanzhun', 'Barlow Condensed ExtraBold', '<PERSON> Condensed ExtraBold Italic'],
        },
        active: resolve,
        inactive: resolve, // Resolve even if inactive to avoid blocking forever
        fontinactive: (fontFamily, fvd) => {
            console.warn(`Font ${fontFamily} with variation ${fvd} failed to load. Font: ${fontFamily}, Variation: ${fvd}`);
        },
    });
});

const $route = useRoute();
const encryptId = computed(() => $route.query.encryptId);
const type = computed(() => $route.query.type as string); // 1 来源于列表，2来源于保存预览
const templateCode = computed(() => $route.query.templateCode);
const reportType = Number($route.query.reportType) || ReportType.PERSONAL;
const fontLoaded = ref(false);

enum ReportType13 {
    性格版 = 1,
    职业角色版 = 2,
    认知版 = 3,
}
enum ReportType7 {
    岗位匹配版 = 1,
    通用版 = 2,
}

interface ComponentsType {
    code: PCode;
    component: any;
    reportType?: number[];
    productName: string;
    extraParam?: Record<string, any>;
    getDataFunctionGroup: any;
}

type PreviewFunction = (productId: { encryptId: string; productId: number }) => Promise<any>;

interface GetDataFunctionGroup {
    1: PreviewFunction;
    2: PreviewFunction;
}

// 默认的 getDataFunctionGroup
const defaultGetDataFuncs: GetDataFunctionGroup = {
    1: ReportService.previewDetialByProductId,
    2: ReportService.previewSaveDetialByProductId,
};

// 工厂函数
function c(
    code: PCode,
    component: any,
    productName: string,
    getDataFunctionGroup: GetDataFunctionGroup = defaultGetDataFuncs,
    reportType?: (ReportType7 | ReportType | ReportType13)[],
    extraParam?: Record<string, any>
): ComponentsType {
    return { code, component, productName, getDataFunctionGroup, reportType, extraParam };
}

const components: ComponentsType[] = [
    c(PCode.CA, ReportUi.ReportPreview1, '胜任力测验', { 1: ReportService.previewDetial, 2: ReportService.previewSaveDetial }),
    c(PCode.HMA, ReportUi.ReportPreview2, '心理健康'),
    c(PCode.OEA, ReportUi.ReportPreview3, '五维性格'),
    c(PCode.CRA, ReportUi.ReportPreview4, '职业韧性'),
    c(PCode.LPA, ReportUi.ReportPreview5, '领导力情景'),
    c(PCode.FCA, ReportUi.ReportPreview6, '金融人才'),
    c(PCode.PTA, ReportUi.ReportPreview7, '16T-职业', defaultGetDataFuncs, [ReportType7.岗位匹配版], { reportType }),
    c(PCode.PTA, ReportUi.ReportPreview7_1, '16T-通用', defaultGetDataFuncs, [ReportType7.通用版], { reportType }),
    c(PCode.OEWA, ReportUi.ReportPreview8, '五维性格w版'),
    c(PCode.OMHA, ReportUi.ReportPreview9, '职业心理健康测评-团队', defaultGetDataFuncs, [ReportType.TEAM], { reportType }),
    c(PCode.OMHA, ReportUi.ReportPreview9_1, '职业心理健康测评-个人、招聘', defaultGetDataFuncs, [ReportType.PERSONAL, ReportType.RECRUITMENT], { reportType }),
    c(PCode.HOTS, ReportUi.ReportPreview10, 'HOTS'),
    c(PCode.DISC, ReportUi.ReportPreview11, '工作风格测评'),
    c(PCode.HIPO, ReportUi.ReportPreview12, '高潜人才识别测评'),
    c(PCode.GBA, ReportUi.ReportPreview13, 'GBA立体人才画像-性格版', defaultGetDataFuncs, [ReportType13.性格版], { reportType }),
    c(PCode.GBA, ReportUi.ReportPreview13_1, 'GBA立体人才画像-职业角色版', defaultGetDataFuncs, [ReportType13.职业角色版], { reportType }),
    c(PCode.GBA, ReportUi.ReportPreview13_2, 'GBA立体人才画像-认知版', defaultGetDataFuncs, [ReportType13.认知版], { reportType }),
    c(PCode.ES, ReportUi.ReportPreview14, '敬业度测评'),
    c(PCode.GPA, ReportUi.ReportPreview15, '笔迹'),
    c(PCode.CAA, ReportUi.ReportPreview16, '认知能力'),
    c(PCode.MA, ReportUi.ReportPreview17, '职业动机'),
    c(PCode.SRC, ReportUi.ReportPreview18, '校招方案组合'),
    c(PCode.NTA, ReportUi.ReportPreview19, '新质人才测评'),
    c(PCode.PQA, ReportUi.ReportPreview20, '政治素养测评'),
    c(PCode.OEWA2, ReportUi.ReportPreview21, '五维性格2.0'),
];

const currentProduct = computed(() => {
    const arr = components.filter((x) => x.code === Number(templateCode.value));
    if (arr.length === 1) {
        return arr[0]!;
    } else {
        return arr.find((x) => x.reportType?.includes(Number(reportType)))!;
    }
});

onMounted(async () => {
    document.documentElement.classList.add('preview-page');
    await fontLoadPromise;
    fontLoaded.value = true;
});
onUnmounted(() => {
    document.documentElement.classList.remove('preview-page');
});
</script>

<style lang="less">
html.preview-page {
    --base-body-height: 'auto';

    #wrap {
        min-width: auto;
    }

    .b-layout-is-wrap {
        height: auto;
    }
}
</style>
